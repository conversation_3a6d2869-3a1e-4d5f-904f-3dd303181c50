import request from '@/utils/request'

// 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）
export function listLitigationCostApproval(query) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/list',
    method: 'get',
    params: query
  })
}

// 查询法诉费用审批详细
export function getLitigationCostApproval(id) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/' + id,
    method: 'get'
  })
}

// 获取法诉案件的费用提交记录详情（用于审批弹窗）
export function getLitigationCostSubmissionRecords(litigationCaseId) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/records/' + litigationCaseId,
    method: 'get'
  })
}

// 单个费用记录审批
export function approveLitigationCostRecord(data) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/approve',
    method: 'put',
    data: data
  })
}

// 批量审批费用记录
export function batchApproveLitigationCostRecords(data) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/batchApprove',
    method: 'put',
    data: data
  })
}

// 获取法诉费用类型字典
export function getLitigationCostTypes() {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/costTypes',
    method: 'get'
  })
}

// 获取审批状态统计
export function getApprovalStatistics() {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/statistics',
    method: 'get'
  })
}

// 开始审批流程
export function startLitigationApprovalFlow(id) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/startApproval/' + id,
    method: 'post'
  })
}

// 审批通过（进入下一个审批节点）
export function approveLitigationCostFlow(data) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/approveFlow',
    method: 'put',
    data: data
  })
}

// 审批拒绝
export function rejectLitigationCostFlow(data) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/rejectFlow',
    method: 'put',
    data: data
  })
}

// 批量审批流程
export function batchApproveLitigationCostFlow(data) {
  return request({
    url: '/litigation_cost_approval/litigation_cost_approval/batchApproveFlow',
    method: 'put',
    data: data
  })
}
